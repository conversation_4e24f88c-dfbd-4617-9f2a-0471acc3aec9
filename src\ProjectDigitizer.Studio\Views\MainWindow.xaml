﻿<Window
    Height="768"
    Title="ProjectDigitizer Studio"
    Width="1024"
    mc:Ignorable="d"
    x:Class="ProjectDigitizer.Studio.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:canvasControls="clr-namespace:ProjectDigitizer.Studio.Controls.Canvas"
    xmlns:commonControls="clr-namespace:ProjectDigitizer.Studio.Controls.Common"
    xmlns:controls="clr-namespace:ProjectDigitizer.Studio.Controls"
    xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:ProjectDigitizer.Studio"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:ProjectDigitizer.Studio.Models"
    xmlns:nodify="clr-namespace:Nodify;assembly=Nodify"
    xmlns:propertiesControls="clr-namespace:ProjectDigitizer.Studio.Controls.Properties"
    xmlns:selectors="clr-namespace:ProjectDigitizer.Studio.Selectors"
    xmlns:viewModels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/NodeTemplates.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!--  常用转换器  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:BooleanToGridLengthConverter x:Key="BooleanToGridLengthConverter"/>
            <converters:BooleanToAngleConverter x:Key="BooleanToAngleConverter"/>
            <converters:MidPointXConverter x:Key="MidPointXConverter"/>
            <converters:MidPointYConverter x:Key="MidPointYConverter"/>
            <converters:GreaterThanConverter x:Key="GreaterThanConverter"/>

            <!--  节点模板选择器  -->
            <selectors:NodeTypeDataTemplateSelector
                ControlNodeTemplate="{StaticResource NodeTemplate}"
                DataCalculationGroupingNodeTemplate="{StaticResource DataCalculationGroupingNodeTemplate}"
                DefaultTemplate="{StaticResource NodeTemplate}"
                InputNodeTemplate="{StaticResource NodeTemplate}"
                OutputNodeTemplate="{StaticResource NodeTemplate}"
                TransformNodeTemplate="{StaticResource NodeTemplate}"
                x:Key="NodeTemplateSelector"/>

            <!--  连接线模板选择器  -->
            <selectors:ConnectionTemplateSelector
                BezierConnectionTemplate="{StaticResource BezierConnectionTemplate}"
                CircuitConnectionTemplate="{StaticResource CircuitConnectionTemplate}"
                DefaultTemplate="{StaticResource DefaultConnectionTemplate}"
                LineConnectionTemplate="{StaticResource LineConnectionTemplate}"
                StepConnectionTemplate="{StaticResource StepConnectionTemplate}"
                x:Key="ConnectionTemplateSelector"/>


            <!--  色彩定义  -->
            <SolidColorBrush Color="#4285F4"
                    x:Key="PrimaryBrush"/>
            <SolidColorBrush Color="#34A853"
                    x:Key="SecondaryBrush"/>
            <SolidColorBrush Color="#F5F5F5"
                    x:Key="BackgroundBrush"/>
            <SolidColorBrush Color="#FFFFFF"
                    x:Key="SurfaceBrush"/>
            <SolidColorBrush Color="#E0E0E0"
                    x:Key="BorderBrush"/>
            <SolidColorBrush Color="#333333"
                    x:Key="TextBrush"/>
            <SolidColorBrush Color="#333333"
                    x:Key="DarkBackgroundBrush"/>

            <!--  现代化按钮样式  -->
            <Style TargetType="Button"
                    x:Key="ModernButtonStyle">
                <Setter Property="Background"
                        Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground"
                        Value="White"/>
                <Setter Property="BorderThickness"
                        Value="0"/>
                <Setter Property="Padding"
                        Value="12,6"/>
                <Setter Property="Margin"
                        Value="5,2"/>
                <Setter Property="FontSize"
                        Value="12"/>
                <Setter Property="Cursor"
                        Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver"
                                        Value="True">
                                    <Setter Property="Background"
                                            Value="#3367D6"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                        Value="True">
                                    <Setter Property="Background"
                                            Value="#2E5BBA"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!--  模板项样式 - 用于Border元素  -->
            <Style TargetType="Border"
                    x:Key="TemplateItemStyle">
                <Setter Property="Background"
                        Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush"
                        Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="Margin"
                        Value="5,2"/>
                <Setter Property="Padding"
                        Value="8"/>
                <Setter Property="Cursor"
                        Value="Hand"/>
                <Setter Property="CornerRadius"
                        Value="3"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect
                            BlurRadius="3"
                            Color="Black"
                            Opacity="0.1"
                            ShadowDepth="1"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver"
                            Value="True">
                        <Setter Property="Background"
                                Value="#F8F9FA"/>
                        <Setter Property="BorderBrush"
                                Value="{StaticResource PrimaryBrush}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>



            <!--  连接线样式  -->
            <Style TargetType="nodify:Connection"
                    x:Key="CustomConnectionStyle">
                <Setter Property="Stroke"
                        Value="#4A90E2"/>
                <Setter Property="StrokeThickness"
                        Value="2.5"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect
                            BlurRadius="4"
                            Color="#4A90E2"
                            Opacity="0.3"
                            ShadowDepth="0"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver"
                            Value="True">
                        <Setter Property="Stroke"
                                Value="#2196F3"/>
                        <Setter Property="StrokeThickness"
                                Value="3"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect
                                    BlurRadius="6"
                                    Color="#2196F3"
                                    Opacity="0.5"
                                    ShadowDepth="0"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger Property="IsSelected"
                            Value="True">
                        <Setter Property="Stroke"
                                Value="#1976D2"/>
                        <Setter Property="StrokeThickness"
                                Value="3"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect
                                    BlurRadius="6"
                                    Color="#1976D2"
                                    Opacity="0.4"
                                    ShadowDepth="0"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!--  挂起连接线样式  -->
            <Style TargetType="nodify:PendingConnection">
                <Setter Property="Stroke"
                        Value="#28A745"/>
                <Setter Property="StrokeThickness"
                        Value="2.5"/>
                <Setter Property="StrokeDashArray"
                        Value="6,3"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect
                            BlurRadius="4"
                            Color="#28A745"
                            Opacity="0.3"
                            ShadowDepth="0"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!--  网格背景定义  -->
            <GeometryDrawing
                Brush="#303030"
                Geometry="M0,0 L0,1 0.03,1 0.03,0.03 1,0.03 1,0 Z"
                x:Key="SmallGridGeometry"/>

            <GeometryDrawing
                Brush="#404040"
                Geometry="M0,0 L0,1 0.015,1 0.015,0.015 1,0.015 1,0 Z"
                x:Key="LargeGridGeometry"/>

            <DrawingBrush
                Drawing="{StaticResource SmallGridGeometry}"
                TileMode="Tile"
                Transform="{Binding ViewportTransform, ElementName=Canvas}"
                Viewport="0 0 20 20"
                ViewportUnits="Absolute"
                x:Key="SmallGridDrawingBrush"/>

            <DrawingBrush
                Drawing="{StaticResource LargeGridGeometry}"
                Opacity="0.5"
                TileMode="Tile"
                Transform="{Binding ViewportTransform, ElementName=Canvas}"
                Viewport="0 0 100 100"
                ViewportUnits="Absolute"
                x:Key="LargeGridDrawingBrush"/>
        </ResourceDictionary>
    </Window.Resources>
    <DockPanel LastChildFill="True">
        <!--  菜单栏  -->
        <materialDesign:ColorZone
            DockPanel.Dock="Top"
            Mode="PrimaryDark"
            Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!--  应用标题和图标  -->
                <StackPanel Grid.Column="0"
                        Orientation="Horizontal">
                    <materialDesign:PackIcon
                        Foreground="{DynamicResource MaterialDesignDarkForeground}"
                        Height="24"
                        Kind="Cog"
                        Margin="0,0,12,0"
                        VerticalAlignment="Center"
                        Width="24"/>
                    <TextBlock
                        FontSize="18"
                        FontWeight="Medium"
                        Foreground="{DynamicResource MaterialDesignDarkForeground}"
                        Text="ProjectDigitizer Studio"
                        VerticalAlignment="Center"/>
                </StackPanel>

                <!--  主菜单  -->
                <Menu
                    Background="Transparent"
                    Grid.Column="1"
                    HorizontalAlignment="Stretch"
                    Margin="32,0,0,0"
                    VerticalAlignment="Center">
                    <MenuItem Foreground="{DynamicResource MaterialDesignDarkForeground}"
                            Header="文件">
                        <MenuItem Header="新建项目"
                                Icon="{materialDesign:PackIcon Kind=FileDocumentPlus}"/>
                        <MenuItem Header="保存项目"
                                Icon="{materialDesign:PackIcon Kind=ContentSave}"/>
                        <MenuItem Header="另存为项目"
                                Icon="{materialDesign:PackIcon Kind=ContentSaveOutline}"/>
                        <Separator/>
                        <MenuItem Header="退出"
                                Icon="{materialDesign:PackIcon Kind=ExitToApp}"/>
                    </MenuItem>
                    <MenuItem Foreground="{DynamicResource MaterialDesignDarkForeground}"
                            Header="模板">
                        <MenuItem Header="导入本地模板"
                                Icon="{materialDesign:PackIcon Kind=Import}"/>
                        <MenuItem Header="导入云端模板"
                                Icon="{materialDesign:PackIcon Kind=CloudDownload}"/>
                        <MenuItem Header="存为本地模板"
                                Icon="{materialDesign:PackIcon Kind=ContentSave}"/>
                        <MenuItem Header="存为云端模板"
                                Icon="{materialDesign:PackIcon Kind=CloudUpload}"/>
                    </MenuItem>
                    <MenuItem Foreground="{DynamicResource MaterialDesignDarkForeground}"
                            Header="数据">
                        <MenuItem Header="CAD数据导入"
                                Icon="{materialDesign:PackIcon Kind=FileOutline}"/>
                        <MenuItem Header="数据库数据导入"
                                Icon="{materialDesign:PackIcon Kind=Database}"/>
                        <MenuItem Header="表格数据导入"
                                Icon="{materialDesign:PackIcon Kind=Table}"/>
                        <MenuItem Header="项目信息"
                                Icon="{materialDesign:PackIcon Kind=Information}"/>
                    </MenuItem>
                    <MenuItem Foreground="{DynamicResource MaterialDesignDarkForeground}"
                            Header="材料表">
                        <MenuItem Header="导出准备"
                                Icon="{materialDesign:PackIcon Kind=Export}"/>
                        <MenuItem Header="生成材料表"
                                Icon="{materialDesign:PackIcon Kind=Table}"/>
                        <MenuItem Header="导出到CAD"
                                Icon="{materialDesign:PackIcon Kind=FileOutline}"/>
                        <MenuItem Header="导出为excel文件"
                                Icon="{materialDesign:PackIcon Kind=FileExcel}"/>
                    </MenuItem>
                    <MenuItem Foreground="{DynamicResource MaterialDesignDarkForeground}"
                            Header="工具">
                        <MenuItem Header="导入批注"
                                Icon="{materialDesign:PackIcon Kind=CommentText}"/>
                        <Separator/>
                        <MenuItem
                            Click="OpenTelerikTemplateDesigner_Click"
                            Header="Telerik模板设计器"
                            Icon="{materialDesign:PackIcon Kind=FileDocument}"/>

                    </MenuItem>
                    <MenuItem Foreground="{DynamicResource MaterialDesignDarkForeground}"
                            Header="帮助">
                        <MenuItem Header="用户手册"
                                Icon="{materialDesign:PackIcon Kind=HelpCircle}"/>
                        <MenuItem Header="关于"
                                Icon="{materialDesign:PackIcon Kind=Information}"/>
                    </MenuItem>
                </Menu>
            </Grid>
        </materialDesign:ColorZone>

        <!--  功能区  -->
        <materialDesign:ColorZone
            DockPanel.Dock="Top"
            Mode="PrimaryLight"
            Padding="16,8">
            <WrapPanel Orientation="Horizontal">
                <Button
                    Background="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="0"
                    Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="新建项目">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Height="16"
                                Kind="FileDocumentPlus"
                                Margin="0,0,4,0"
                                Width="16"/>
                            <TextBlock Text="新建"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button
                    Background="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="0"
                    Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="保存项目">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Height="16"
                                Kind="ContentSave"
                                Margin="0,0,4,0"
                                Width="16"/>
                            <TextBlock Text="保存"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator Margin="8,0"/>

                <Button
                    Background="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="0"
                    Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="导入CAD数据">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Height="16"
                                Kind="FileOutline"
                                Margin="0,0,4,0"
                                Width="16"/>
                            <TextBlock Text="导入CAD"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator Margin="8,0"/>

                <Button
                    Background="Transparent"
                    BorderBrush="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="1"
                    Click="BtnEnableAll_Click"
                    Content="全部启用"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="启用所有子模板"
                    x:Name="BtnEnableAll"/>

                <Button
                    Background="Transparent"
                    BorderBrush="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="1"
                    Click="BtnDisableAll_Click"
                    Content="全部关闭"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="关闭所有子模板"
                    x:Name="BtnDisableAll"/>

                <Button
                    Background="Transparent"
                    BorderBrush="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="1"
                    Click="BtnClearCanvas_Click"
                    Content="清空画布"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="清空所有子模板"
                    x:Name="BtnClearCanvas"/>

                <Button
                    Background="Transparent"
                    BorderBrush="{DynamicResource MaterialDesignPrimary}"
                    BorderThickness="1"
                    Click="BtnDeleteSelected_Click"
                    Content="删除选中"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="删除选中的子模板"
                    x:Name="BtnDeleteSelected"/>

                <Separator Margin="8,0"/>

                <Button
                    Background="Transparent"
                    BorderThickness="0"
                    Click="BtnHideClosedModules_Click"
                    Content="隐藏关闭的模板"
                    Foreground="{DynamicResource MaterialDesignBody}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    ToolTip="隐藏所有关闭的模板"
                    x:Name="BtnHideClosedModules"/>
            </WrapPanel>
        </materialDesign:ColorZone>

        <!--  状态栏  -->
        <materialDesign:ColorZone
            DockPanel.Dock="Bottom"
            Mode="PrimaryLight"
            Padding="16,4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                        Orientation="Horizontal">
                    <materialDesign:PackIcon
                        Foreground="Green"
                        Height="16"
                        Kind="CheckCircle"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Width="16"/>
                    <TextBlock
                        Foreground="{DynamicResource MaterialDesignBody}"
                        Text="就绪"
                        VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1"
                        Orientation="Horizontal">
                    <TextBlock
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        Text="节点数量: "
                        VerticalAlignment="Center"/>
                    <TextBlock
                        Foreground="{DynamicResource MaterialDesignBody}"
                        Margin="0,0,16,0"
                        Text="{Binding Nodes.Count}"
                        VerticalAlignment="Center"/>
                    <TextBlock
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        Text="连接数量: "
                        VerticalAlignment="Center"/>
                    <TextBlock
                        Foreground="{DynamicResource MaterialDesignBody}"
                        Text="{Binding Connections.Count}"
                        VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!--  主体内容  -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="{Binding IsPropertyPanelVisible, Converter={StaticResource BooleanToGridLengthConverter}}"/>
            </Grid.ColumnDefinitions>

            <!--  左侧子模板选择区域  -->
            <materialDesign:Card
                Grid.Column="0"
                Margin="8"
                materialDesign:ElevationAssist.Elevation="Dp4">
                <DockPanel Background="{DynamicResource MaterialDesignPaper}">
                    <!--  模板选择器头部  -->
                    <materialDesign:ColorZone
                        DockPanel.Dock="Top"
                        Mode="PrimaryMid"
                        Padding="16">
                        <StackPanel>
                            <TextBlock
                                FontSize="18"
                                FontWeight="Medium"
                                Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                Text="工作流模块库"/>
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                Margin="0,4,0,0"
                                Opacity="0.7"
                                Text="拖拽模块到画布创建工作流"/>
                        </StackPanel>
                    </materialDesign:ColorZone>

                    <!--  动态模板分类列表  -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                            materialDesign:ScrollViewerAssist.IsAutoHideEnabled="True">
                        <ItemsControl ItemsSource="{Binding TemplateCategories}"
                                Margin="12">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,0,0,16"
                                            materialDesign:ElevationAssist.Elevation="Dp2">
                                        <StackPanel>
                                            <!--  分类标题  -->
                                            <materialDesign:ColorZone Mode="PrimaryLight"
                                                    Padding="12,8">
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon
                                                        Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                                        Height="20"
                                                        Kind="Widgets"
                                                        Margin="0,0,8,0"
                                                        Width="20"/>
                                                    <TextBlock
                                                        FontSize="14"
                                                        FontWeight="Medium"
                                                        Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                                        Text="{Binding Name}"/>
                                                </StackPanel>
                                            </materialDesign:ColorZone>

                                            <!--  分类中的模板项  -->
                                            <ItemsControl ItemsSource="{Binding Items}"
                                                    Margin="8">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border
                                                            Background="Transparent"
                                                            BorderThickness="0"
                                                            Cursor="Hand"
                                                            Margin="0,2"
                                                            MouseDown="TemplateItem_MouseDown"
                                                            MouseMove="TemplateItem_MouseMove"
                                                            MouseUp="TemplateItem_MouseUp"
                                                            Padding="12,8"
                                                            ToolTip="{Binding Description}">
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="Background"
                                                                            Value="Transparent"/>
                                                                    <Style.Triggers>
                                                                        <Trigger Property="IsMouseOver"
                                                                                Value="True">
                                                                            <Setter Property="Background"
                                                                                    Value="#F0F0F0"/>
                                                                        </Trigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                </Grid.ColumnDefinitions>

                                                                <!--  模块图标  -->
                                                                <Border
                                                                    CornerRadius="4,0,0,4"
                                                                    Grid.Column="0"
                                                                    Height="32"
                                                                    Margin="0,0,12,0"
                                                                    Width="8">
                                                                    <Border.Background>
                                                                        <SolidColorBrush Color="{Binding DataContext.Color, RelativeSource={RelativeSource AncestorType=ItemsControl}}"/>
                                                                    </Border.Background>
                                                                </Border>

                                                                <!--  模块信息  -->
                                                                <StackPanel Grid.Column="1">
                                                                    <TextBlock
                                                                        FontSize="13"
                                                                        FontWeight="Medium"
                                                                        Foreground="{DynamicResource MaterialDesignBody}"
                                                                        Text="{Binding Name}"/>
                                                                    <TextBlock
                                                                        FontSize="11"
                                                                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                        Margin="0,2,0,0"
                                                                        Text="{Binding Description}"
                                                                        TextWrapping="Wrap"/>
                                                                </StackPanel>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </StackPanel>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </DockPanel>
            </materialDesign:Card>

            <!--  右侧主要内容区域  -->
            <TabControl Grid.Column="1"
                    Margin="5">
                <TabItem Header="模板">
                    <!--  画布区域  -->
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!--  画布操作工具栏  -->
                        <ToolBar Grid.Row="0">
                            <Button
                                Click="BtnEnableAll_Click"
                                Content="全部启用"
                                ToolTip="启用所有子模板"
                                x:Name="BtnEnableAllCanvas"/>
                            <Button
                                Click="BtnDisableAll_Click"
                                Content="全部关闭"
                                ToolTip="关闭所有子模板"
                                x:Name="BtnDisableAllCanvas"/>
                            <Button
                                Click="BtnClearCanvas_Click"
                                Content="清空画布"
                                ToolTip="清空所有子模板"
                                x:Name="BtnClearCanvasArea"/>
                            <Button
                                Click="BtnDeleteSelected_Click"
                                Content="删除选中"
                                ToolTip="删除选中的子模板"
                                x:Name="BtnDeleteSelectedCanvas"/>
                            <Separator/>
                            <Button
                                Click="BtnHideClosedModules_Click"
                                Content="隐藏关闭的模板"
                                ToolTip="隐藏所有关闭的模板"
                                x:Name="BtnHideClosedModulesCanvas"/>
                            <Separator/>
                            <!--  自动布局功能按钮  -->
                            <Button
                                Click="BtnAutoLayoutHierarchical_Click"
                                Content="自动布局"
                                ToolTip="按照数据流方向自动排列节点，支持多行换行"
                                x:Name="BtnAutoLayoutHierarchical">
                                <Button.Style>
                                    <Style BasedOn="{StaticResource {x:Type Button}}"
                                            TargetType="Button">
                                        <Setter Property="Background"
                                                Value="#E3F2FD"/>
                                        <Setter Property="BorderBrush"
                                                Value="#2196F3"/>
                                        <Setter Property="Foreground"
                                                Value="#1976D2"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver"
                                                    Value="True">
                                                <Setter Property="Background"
                                                        Value="#BBDEFB"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                            <Separator/>
                            <!--  连接线样式选择  -->
                            <Label
                                Content="连线样式:"
                                Margin="5,0"
                                VerticalAlignment="Center"/>
                            <ComboBox
                                SelectedValue="{Binding CurrentConnectionLineStyle, Mode=TwoWay}"
                                SelectedValuePath="Value"
                                ToolTip="选择连接线的显示样式"
                                VerticalAlignment="Center"
                                Width="120"
                                x:Name="ConnectionStyleComboBox">
                                <ComboBox.ItemsSource>
                                    <x:Array Type="{x:Type models:ConnectionLineStyleItem}">
                                        <!--  正交线 - 暂时注释掉  -->
                                        <!--<models:ConnectionLineStyleItem DisplayName="正交线"
                                                                        Value="Line"
                                                                        Icon="📐"/>-->
                                        <models:ConnectionLineStyleItem
                                            DisplayName="贝塞尔曲线"
                                            Icon="〰️"
                                            Value="Bezier"/>
                                        <models:ConnectionLineStyleItem
                                            DisplayName="阶梯线"
                                            Icon="🔲"
                                            Value="Step"/>
                                        <!--  电路线 - 暂时注释掉  -->
                                        <!--<models:ConnectionLineStyleItem DisplayName="电路线"
                                                                        Value="Circuit"
                                                                        Icon="⚡"/>-->
                                    </x:Array>
                                </ComboBox.ItemsSource>
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock
                                                Margin="0,0,5,0"
                                                Text="{Binding Icon}"
                                                VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding DisplayName}"
                                                    VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </ToolBar>

                        <!--  Nodify画布  -->
                        <Grid Background="{StaticResource SurfaceBrush}"
                                Grid.Row="1">
                            <!--  网格背景  -->
                            <Rectangle
                                Fill="#F8F9FA"
                                Opacity="0.3"
                                Stroke="#E0E0E0"
                                StrokeDashArray="5,5"
                                StrokeThickness="1"/>

                            <nodify:NodifyEditor
                                AllowDrop="True"
                                Background="#F5F5F5"
                                ConnectionCompletedCommand="{Binding CreateConnectionCommand}"
                                Connections="{Binding Connections}"
                                DisconnectConnectorCommand="{Binding DisconnectConnectorCommand}"
                                DragOver="Canvas_DragOver"
                                Drop="Canvas_Drop"
                                ItemTemplateSelector="{StaticResource NodeTemplateSelector}"
                                ItemsSource="{Binding Nodes}"
                                PendingConnection="{Binding PendingConnection}"
                                PreviewMouseDown="Canvas_PreviewMouseDown"
                                RemoveConnectionCommand="{Binding RemoveConnectionCommand}"
                                SelectedItems="{Binding SelectedItems}"
                                SelectionChanged="Canvas_SelectionChanged"
                                ViewportUpdated="Canvas_ViewportUpdated"
                                x:Name="Canvas">

                                <!--  连接线模板 - 包含序号标识的Canvas结构，支持样式切换  -->
                                <nodify:NodifyEditor.ConnectionTemplate>
                                    <DataTemplate>
                                        <!--  使用Canvas来精确定位连接线和序号标识  -->
                                        <Canvas>
                                            <!--  透明的Hit Test区域 - 扩大点击范围到8像素  -->
                                            <nodify:LineConnection
                                                DirectionalArrowsCount="0"
                                                MouseRightButtonDown="Connection_MouseRightButtonDown"
                                                Source="{Binding Source.Anchor}"
                                                Stroke="Transparent"
                                                StrokeThickness="8"
                                                Target="{Binding Target.Anchor}"
                                                x:Name="HitTestConnection"/>

                                            <!--  动态连接线 - 使用ContentPresenter和模板选择器  -->
                                            <ContentPresenter Content="{Binding}"
                                                    ContentTemplateSelector="{StaticResource ConnectionTemplateSelector}"/>

                                            <!--  备用可见连接线 - 当动态连接线失败时使用，默认隐藏  -->
                                            <nodify:LineConnection
                                                DirectionalArrowsCount="0"
                                                IsHitTestVisible="False"
                                                Opacity="0.85"
                                                Source="{Binding Source.Anchor}"
                                                Stroke="{Binding ConnectionColor}"
                                                StrokeThickness="2.5"
                                                Target="{Binding Target.Anchor}"
                                                Visibility="Collapsed"
                                                x:Name="VisualConnection">
                                                <nodify:LineConnection.Style>
                                                    <Style TargetType="nodify:LineConnection">
                                                        <Style.Triggers>
                                                            <!--  禁用状态  -->
                                                            <DataTrigger Binding="{Binding IsEnabled}"
                                                                    Value="False">
                                                                <Setter Property="Stroke"
                                                                        Value="#9E9E9E"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.4"/>
                                                                <Setter Property="StrokeDashArray"
                                                                        Value="6,4"/>
                                                            </DataTrigger>
                                                            <!--  鼠标悬停状态 - 基于Hit Test连接线的悬停状态  -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                            Value="True"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsMouseOver}"
                                                                            Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#1976D2"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3.5"/>
                                                                <Setter Property="Opacity"
                                                                        Value="1.0"/>
                                                            </MultiDataTrigger>
                                                            <!--  选中状态 - 基于Hit Test连接线的选中状态  -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                            Value="True"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsSelected}"
                                                                            Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#FF9800"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3.5"/>
                                                                <Setter Property="Opacity"
                                                                        Value="1.0"/>
                                                            </MultiDataTrigger>
                                                            <!--  禁用状态下的鼠标悬停 - 基于Hit Test连接线的悬停状态  -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                            Value="False"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsMouseOver}"
                                                                            Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#BDBDBD"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.6"/>
                                                            </MultiDataTrigger>
                                                            <!--  禁用状态下的选中 - 基于Hit Test连接线的选中状态  -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                            Value="False"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsSelected}"
                                                                            Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#FF9800"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.7"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3.5"/>
                                                            </MultiDataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </nodify:LineConnection.Style>
                                            </nodify:LineConnection>

                                            <!--  连接序号标识 - 显示在连接线中点  -->
                                            <Border
                                                Background="{Binding ConnectionColor}"
                                                BorderBrush="White"
                                                BorderThickness="2"
                                                CornerRadius="8"
                                                Height="16"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Visibility="{Binding Source.HasMultipleConnections, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                Width="16"
                                                x:Name="ConnectionIndexBadge">
                                                <Border.Effect>
                                                    <DropShadowEffect
                                                        BlurRadius="3"
                                                        Color="#000000"
                                                        Opacity="0.3"
                                                        ShadowDepth="1"/>
                                                </Border.Effect>
                                                <TextBlock
                                                    FontSize="9"
                                                    FontWeight="Bold"
                                                    Foreground="White"
                                                    HorizontalAlignment="Center"
                                                    Text="{Binding ConnectionIndex}"
                                                    VerticalAlignment="Center"/>
                                            </Border>

                                            <!--  连接序号标识 - 跟随连接线动态定位  -->
                                            <Border
                                                Background="{Binding ConnectionColor}"
                                                BorderBrush="White"
                                                BorderThickness="2"
                                                CornerRadius="8"
                                                Height="16"
                                                Width="16">
                                                <Border.RenderTransform>
                                                    <TranslateTransform>
                                                        <TranslateTransform.X>
                                                            <MultiBinding Converter="{StaticResource MidPointXConverter}">
                                                                <Binding Path="Source.Anchor.X"/>
                                                                <Binding Path="Target.Anchor.X"/>
                                                            </MultiBinding>
                                                        </TranslateTransform.X>
                                                        <TranslateTransform.Y>
                                                            <MultiBinding Converter="{StaticResource MidPointYConverter}">
                                                                <Binding Path="Source.Anchor.Y"/>
                                                                <Binding Path="Target.Anchor.Y"/>
                                                            </MultiBinding>
                                                        </TranslateTransform.Y>
                                                    </TranslateTransform>
                                                </Border.RenderTransform>
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Visibility"
                                                                Value="Collapsed"/>
                                                        <Style.Triggers>
                                                            <!--  基于连接器连接数量显示序号标识  -->
                                                            <DataTrigger Binding="{Binding Source.ConnectionCount, Converter={StaticResource GreaterThanConverter}, ConverterParameter=1}"
                                                                    Value="True">
                                                                <Setter Property="Visibility"
                                                                        Value="Visible"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <Border.Effect>
                                                    <DropShadowEffect
                                                        BlurRadius="3"
                                                        Color="#000000"
                                                        Opacity="0.3"
                                                        ShadowDepth="1"/>
                                                </Border.Effect>
                                                <TextBlock
                                                    FontSize="9"
                                                    FontWeight="Bold"
                                                    Foreground="White"
                                                    HorizontalAlignment="Center"
                                                    Text="{Binding ConnectionIndex}"
                                                    VerticalAlignment="Center"/>
                                            </Border>
                                        </Canvas>
                                    </DataTemplate>
                                </nodify:NodifyEditor.ConnectionTemplate>

                                <!--  挂起连接模板  -->
                                <nodify:NodifyEditor.PendingConnectionTemplate>
                                    <DataTemplate>
                                        <nodify:PendingConnection
                                            AllowOnlyConnectors="True"
                                            CompletedCommand="{Binding FinishCommand}"
                                            Opacity="0.8"
                                            StartedCommand="{Binding StartCommand}"
                                            Stroke="#4CAF50"
                                            StrokeDashArray="8,4"
                                            StrokeThickness="2.5">
                                            <nodify:PendingConnection.Effect>
                                                <DropShadowEffect
                                                    BlurRadius="6"
                                                    Color="#4CAF50"
                                                    Opacity="0.4"
                                                    ShadowDepth="0"/>
                                            </nodify:PendingConnection.Effect>
                                        </nodify:PendingConnection>
                                    </DataTemplate>
                                </nodify:NodifyEditor.PendingConnectionTemplate>

                                <!--  节点容器样式 - 完全覆盖Nodify默认样式  -->
                                <nodify:NodifyEditor.ItemContainerStyle>
                                    <Style TargetType="nodify:ItemContainer">
                                        <Setter Property="Location"
                                                Value="{Binding Location}"/>

                                        <Setter Property="BorderThickness"
                                                Value="0"/>
                                        <Setter Property="SelectedBorderThickness"
                                                Value="0"/>
                                        <Setter Property="BorderBrush"
                                                Value="Transparent"/>
                                        <Setter Property="SelectedBrush"
                                                Value="Transparent"/>
                                        <Setter Property="Background"
                                                Value="Transparent"/>
                                        <Setter Property="Foreground"
                                                Value="Transparent"/>

                                        <Setter Property="Effect"
                                                Value="{x:Null}"/>
                                        <Setter Property="Opacity"
                                                Value="1"/>

                                        <Setter Property="Margin"
                                                Value="0"/>
                                        <Setter Property="Padding"
                                                Value="0"/>

                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="nodify:ItemContainer">
                                                    <Border
                                                        Background="Transparent"
                                                        BorderBrush="Transparent"
                                                        BorderThickness="0"
                                                        Margin="0">
                                                        <ContentPresenter/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>

                                        <EventSetter Event="Selected"
                                                Handler="ItemContainer_Selected"/>
                                        <EventSetter Event="MouseDown"
                                                Handler="ItemContainer_MouseDown"/>
                                        <EventSetter Event="MouseDoubleClick"
                                                Handler="ItemContainer_MouseDoubleClick"/>

                                        <Style.Triggers>
                                            <!--  锁定状态下的视觉提示 - 改为手型光标，表示可以交互但位置锁定  -->
                                            <DataTrigger Binding="{Binding IsLocked}"
                                                    Value="True">
                                                <Setter Property="Cursor"
                                                        Value="Hand"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </nodify:NodifyEditor.ItemContainerStyle>
                            </nodify:NodifyEditor>

                            <!--  画布缩放控制器  -->
                            <canvasControls:CanvasZoomControl
                                FitToCanvasRequested="ZoomControl_FitToCanvas"
                                HorizontalAlignment="Right"
                                Margin="16"
                                ResetZoomRequested="ZoomControl_ResetZoom"
                                VerticalAlignment="Bottom"
                                ZoomChanged="ZoomControl_ZoomChanged"
                                x:Name="ZoomControl"/>
                        </Grid>
                    </Grid>
                </TabItem>
                <TabItem Header="说明">
                    <TextBlock Margin="10"
                            Text="说明内容"/>
                </TabItem>
                <TabItem Header="项目信息">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            Grid.Row="0"
                            Margin="0,0,10,5"
                            Text="项目名称:"/>
                        <TextBox
                            Grid.Column="1"
                            Grid.Row="0"
                            Margin="0,0,0,5"/>

                        <TextBlock
                            Grid.Column="0"
                            Grid.Row="1"
                            Margin="0,0,10,0"
                            Text="项目描述:"
                            VerticalAlignment="Top"/>
                        <TextBox
                            AcceptsReturn="True"
                            Grid.Column="1"
                            Grid.Row="1"
                            TextWrapping="Wrap"
                            VerticalAlignment="Stretch"/>
                    </Grid>
                </TabItem>
                <TabItem Header="材料偏好">
                    <TextBlock Margin="10"
                            Text="材料偏好设置"/>
                </TabItem>
                <TabItem Header="材料表">
                    <TextBlock Margin="10"
                            Text="材料表内容"/>
                </TabItem>
            </TabControl>

            <!--  右侧属性面板  -->
            <Grid Grid.Column="2">
                <propertiesControls:DynamicPropertyPanel
                    CanvasViewModel="{Binding}"
                    CurrentNode="{Binding SelectedNode}"
                    Visibility="{Binding IsPropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                    x:Name="PropertyPanel"/>
            </Grid>

            <!--  进度指示器覆盖层  -->
            <commonControls:ProgressIndicator
                Grid.ColumnSpan="3"
                HorizontalAlignment="Right"
                Margin="20"
                MaxWidth="400"
                Panel.ZIndex="1000"
                VerticalAlignment="Top"
                x:Name="ProgressIndicator"/>
        </Grid>
    </DockPanel>
</Window>
